uuid: 3ce86306-8b41-4ad4-b2c0-6fd38ccc19da
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.coupon_card.field_coupon
    - field.field.paragraph.coupon_card.field_month
    - field.field.paragraph.coupon_card.field_new_p
    - field.field.paragraph.coupon_card.field_old_price
    - paragraphs.paragraphs_type.coupon_card
id: paragraph.coupon_card.default
targetEntityType: paragraph
bundle: coupon_card
mode: default
content:
  field_coupon:
    type: string_textfield
    weight: 3
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_month:
    type: options_select
    weight: 0
    region: content
    settings: {  }
    third_party_settings: {  }
  field_new_p:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_old_price:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
hidden:
  created: true
