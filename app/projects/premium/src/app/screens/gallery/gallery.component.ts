import {
  <PERSON>mpo<PERSON>,
  ElementRef,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Query<PERSON>ist,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { CdnPipe, distinctJSON } from '@pb/ui';
import { VideoPlayerComponent } from '@pb/ui/components/video-player/video-player.component';
import { ZoomableImageComponent } from '@pb/ui/components/zoomable-image/zoomable-image.component';
import { GetCDNImage } from '@pb/ui/pipes/cdn.pipe';
import { Apollo } from 'apollo-angular';
import {
  BehaviorSubject,
  combineLatest,
  Observable,
  of,
  Subscription,
} from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  first,
  map,
  switchMap,
  take,
} from 'rxjs/operators';

import lgAutoplay from 'lightgallery/plugins/autoplay';
import lgFullscreen from 'lightgallery/plugins/fullscreen';
import lgThumbnail from 'lightgallery/plugins/thumbnail';
import lgVideo from 'lightgallery/plugins/video';
import lgZoom from 'lightgallery/plugins/zoom';

import { Location, AsyncPipe } from '@angular/common';
import { LightgalleryModule } from 'lightgallery/angular';
import { InitDetail } from 'lightgallery/lg-events';
import { LightGallery } from 'lightgallery/lightgallery';
import { AnalyticsService } from '../../services/analytics.service';
import { FavoritesService } from '../../services/favorites/favorites.service';
import { FavoriteStarComponent } from '../../shared';
import {
  IGirlInfoSlideshowData,
  IGirlInfoSlideshowFieldMediaSlideshow,
  INexxVideoItem,
} from './definitions/models';
import { GIRL_INFO_SLIDESHOW_DATA_QUERY } from './definitions/queries';
import { AccountService } from '../../services/account.service';

type Item =
  | INexxVideoItem
  | (IGirlInfoSlideshowFieldMediaSlideshow & { image: HTMLImageElement });

@Component({
  selector: 'app-gallery',
  templateUrl: './gallery.component.html',
  styleUrls: ['./gallery.component.css'],
  imports: [
    AsyncPipe,
    LightgalleryModule,
    CdnPipe,
    FavoriteStarComponent,
    RouterLink,
  ],
})
export class GalleryComponent implements OnDestroy {
  $girlInfoId: Observable<string>;
  $girlInfo: Observable<IGirlInfoSlideshowData>;
  $mid: Observable<number>;
  $hash: Observable<string>;
  $item: Observable<Item>;
  $index: Observable<number>;
  girlId: string;
  private itemsBehav = new BehaviorSubject<Item[]>([]);
  $items = this.itemsBehav.asObservable();
  mid: number;
  hash: string;
  return: string = null;

  error?: Error;
  settings = undefined;
  private lightGallery!: LightGallery;

  onInit = (detail: InitDetail): void => {
    this.lightGallery = detail.instance;
    this.lightGallery.openGallery();
  };
  private autoPlay = new BehaviorSubject<number>(-1);
  private autoPlaySub: Subscription;

  public get AutoPlayObs(): Observable<number> {
    return this.autoPlay.asObservable();
  }

  private navigationSub?: Subscription;

  $prevLink: Observable<[string, string, string | number] | undefined>;
  $nextLink: Observable<[string, string, string | number] | undefined>;

  public prev() {
    this.navigationSub?.unsubscribe();
    this.navigationSub = this.$prevLink
      .pipe(
        first(),
        filter((v) => !!v),
      )
      .subscribe((v) => this.router.navigate(v));
  }

  public next() {
    this.navigationSub?.unsubscribe();
    this.navigationSub = this.$nextLink
      .pipe(
        first(),
        filter((v) => !!v),
      )
      .subscribe((v) => this.router.navigate(v));
  }

  @HostListener('window:keyup', ['$event'])
  keyDown(e: KeyboardEvent) {
    switch (e.key) {
      case 'ArrowLeft':
        this.prev();
        break;
      case 'ArrowRight':
        this.next();
        break;
      case 'ArrowDown':
        this.showPagination = false;
        break;
      case 'ArrowUp':
        this.showPagination = true;
        break;
      case 'Escape':
        combineLatest([
          of('/girl'),
          this.$girlInfo.pipe(
            map((v) => v?.girlInfoById?.queryGirl?.entities[0].id),
          ),
          this.$girlInfoId,
        ])
          .pipe(
            first(),
            filter((v) => v.filter((i) => !!i).length === 3),
          )
          .subscribe((v) => this.router.navigate(v));
        break;
    }
  }

  zoom(number: number) {
    this.image?.zoom(number);
  }

  $filter: Observable<'images' | 'videos' | 'all'>;
  $normalizedVideoLink: Observable<
    [string, string, string | number] | undefined
  >;
  $normalizedImageLink: Observable<
    [string, string, string | number] | undefined
  >;

  @ViewChildren('thumbnail') thumbnails: QueryList<HTMLElement>;

  @ViewChild('zoomableImage') image: ZoomableImageComponent;
  @ViewChild('videoPlayer') video: VideoPlayerComponent;

  private readonly sub: Subscription;
  private readonly itemsSubscription: Subscription;
  showPagination = true;

  constructor(
    public favoritesService: FavoritesService,
    private router: Router,
    private route: ActivatedRoute,
    private readonly ele: ElementRef<HTMLElement>,
    private meta: Meta,
    private location: Location,
    apollo: Apollo,
    titleService: Title,
    analyticsService: AnalyticsService,
    private accountService: AccountService,
  ) {
    titleService.setTitle(`Galerie | Playboy All Access`);
    this.girlId = route.snapshot.paramMap.get('girl_info_id');

    let previousGirlInfoId;
    route.params.subscribe((params) => {
      if (previousGirlInfoId !== params.girl_info_id) {
        analyticsService.sendEvent('girl_info', params.girl_info_id);
        previousGirlInfoId = params.girl_info_id;
      }
      analyticsService.sendEvent('media', params.resource_id);
    });

    this.$filter = route.queryParamMap.pipe(
      map((v) => v.get('filter')),
      map((v) =>
        v === 'images' || v === 'videos'
          ? v
          : ('all' as 'images' | 'videos' | 'all'),
      ),
      distinctUntilChanged((a, b) => a === b),
    );

    this.$girlInfoId = route.paramMap.pipe(
      map((v) => v.get('girl_info_id')),
      distinctJSON(),
      filter((v) => !!v),
    );

    this.$girlInfo = this.$girlInfoId.pipe(
      switchMap((girlInfoId) =>
        apollo.query<IGirlInfoSlideshowData>({
          query: GIRL_INFO_SLIDESHOW_DATA_QUERY,
          variables: { girlInfoId },
        }),
      ),
      map((v) => v.data),
    );

    this.$girlInfo
      .pipe(take(1))
      .subscribe((v) =>
        titleService.setTitle(
          `${v.girlInfoById.queryGirl.entities[0].name} | Galerie | Playboy All Access`,
        ),
      );

    this.itemsSubscription = combineLatest([this.$filter, this.$girlInfo])
      .pipe(
        map(([filter, v]) => {
          const isGirlOfTheDay = v.girlInfoById.reverseFieldGirlInfosNode?.entities &&
            v.girlInfoById.reverseFieldGirlInfosNode.entities.length > 0;
          return v.girlInfoById.queryGalleries.entities
            .filter((v) => !!v)
            .reverse()
            .reduce((all, curr) => {
              const images = (curr?.fieldMediaSlideshow || []).filter(
                (v) => !!v,
              );
              const filteredVideos =  (curr?.fieldVideos || []).filter((v) => !!v);
              const videos = this.accountService.subscriptionType() === 'plus' && isGirlOfTheDay ? [] : filteredVideos;

              switch (filter) {
                case 'images':
                  return [...all, ...images];
                case 'videos':
                  return [...all, ...videos];
                default:
                  return [...all, ...images, ...videos];
              }
            }, [])
            .filter((v) => !!v.entity);
        }),
        map((items) =>
          items
            .filter(
              (v) =>
                !!v &&
                !!v?.entity &&
                (v.entity?.fieldMediaImage?.url ||
                  v.entity?.fieldPreviewImage?.entity?.fieldMediaImage?.url),
            )
            .map((item) => {
              if (item.entity?.fieldMediaImage?.url) {
                const image = new Image();
                image.src = GetCDNImage(item?.entity?.fieldMediaImage?.url);
                return { ...item, image };
              }
              return item;
            }),
        ),
        distinctJSON(),
      )
      .subscribe((items) => {
        this.itemsBehav.next(items);
        // items.find(element => /* aktuelle MID aus URL */ == element.entity.mid)
      });

    this.$mid = route.paramMap.pipe(
      map((v) => parseInt(v.get('resource_id'), 10)),
      distinctJSON(),
      filter((v) => !isNaN(v)),
    );

    this.$index = combineLatest([this.$items, this.$mid]).pipe(
      map(([items, mid]) => {
        return items.findIndex((v: INexxVideoItem) => v?.entity?.mid === mid);
      }),
      distinctJSON(),
      filter((v) => !isNaN(v)),
    );

    this.$item = combineLatest([this.$items, this.$index]).pipe(
      map(([articles, index]) =>
        articles[index]?.entity ? articles[index] : undefined,
      ),
      distinctJSON(),
    );

    this.sub = this.$girlInfo
      .pipe(
        filter((v) => !!v),
        switchMap((v) =>
          combineLatest([
            this.$girlInfoId,
            this.$index,
            this.$item,
            this.$items,
          ]),
        ),
        debounceTime(100),
      )
      .subscribe(([girlInfoId, index, item, items]) => {
        this.image?.reset();
        if (this.settings) {
          this.lightGallery.slide(index);
        } else {
          this.settings = {
            counter: true,
            zoom: true,
            showMaximizeIcon: false,
            download: false,
            plugins: [lgZoom, lgThumbnail, lgAutoplay, lgFullscreen, lgVideo],
            preload: 3,
            closable: false,
            scale: 1,
            showZoomInOutIcons: true,
            toggleThumb: true,
            allowMediaOverlap: false,
            actualSize: true,
            actualSizeIcons: {
              zoomIn: 'lg-actual-size',
              zoomOut: 'lg-actual-size',
            },
            licenseKey: 'BFDD61E7-12C644A7-BD135AF3-22677728',
            escKey: false,
            enableSwipe: true,
            enableDrag: true,
            appendSubHtmlTo: '.lg-item',
            index,
          };
        }
      });

    this.$prevLink = combineLatest([
      of('/girl-info'),
      this.$girlInfoId,
      combineLatest([
        this.$items.pipe(map((v) => v.map((e) => e.entity.mid))),
        this.$mid,
      ]).pipe(
        map(
          ([itemMIDS, mid]) =>
            itemMIDS[
              itemMIDS.indexOf(mid) >= 0 ? itemMIDS.indexOf(mid) - 1 : undefined
            ],
        ),
      ), // I KNOW ITS CONVOLUTED
    ]).pipe(map((v) => (v.filter((a) => !!a).length === 3 ? v : undefined)));
    this.$nextLink = combineLatest([
      of('/girl-info'),
      this.$girlInfoId,
      combineLatest([
        this.$items.pipe(map((v) => v.map((e) => e.entity.mid))),
        this.$mid,
      ]).pipe(
        map(
          ([itemMIDS, mid]) =>
            itemMIDS[
              itemMIDS.indexOf(mid) >= 0 ? itemMIDS.indexOf(mid) + 1 : undefined
            ],
        ),
      ), // I KNOW ITS CONVOLUTED
    ]).pipe(map((v) => (v.filter((a) => !!a).length === 3 ? v : undefined)));

    this.$normalizedVideoLink = combineLatest([
      of('/girl-info'),
      this.$girlInfoId,
      combineLatest([this.$girlInfo, this.$item]).pipe(
        map(([v, current]) =>
          !!(current as INexxVideoItem).entity.fieldNexxId
            ? current.entity.mid
            : v.girlInfoById.queryGalleries.entities.find(
                (v) => !!v.fieldVideos && v.fieldVideos.length > 0,
              )?.fieldVideos[0]?.entity.mid,
        ),
      ),
    ]).pipe(map((link) => (link[2] === undefined ? undefined : link)));
    this.$normalizedImageLink = combineLatest([
      of('/girl-info'),
      this.$girlInfoId,
      combineLatest([this.$girlInfo, this.$item]).pipe(
        map(([v, current]) =>
          !!(current as IGirlInfoSlideshowFieldMediaSlideshow).entity
            .fieldMediaImage
            ? current.entity.mid
            : v.girlInfoById.queryGalleries.entities.find(
                (v) =>
                  !!v.fieldMediaSlideshow && v.fieldMediaSlideshow.length > 0,
              )?.fieldMediaSlideshow[0]?.entity.mid,
        ),
        distinctJSON(),
      ),
    ]);

    let vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
    console.log(vh);
    window.addEventListener('resize', () => {
      let vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    });
  }

  ngOnInit(): void {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    this.route.queryParams.subscribe((params) => {
      if (!!params.return) {
        this.return = params.return;
      }
    });
    this.meta.removeTag(`name='og:site_name'`);
    this.meta.removeTag(`name='og:type'`);
    this.meta.removeTag(`name='og:title'`);
    this.meta.removeTag(`name='og:description'`);
    this.meta.removeTag(`name='og:locale'`);
    this.meta.removeTag(`name='og:url'`);
    this.meta.removeTag(`name='og:image'`);
    this.meta.removeTag(`name='article:published_time'`);
    this.meta.removeTag(`name='profile:first_name'`);
    this.meta.removeTag(`name='profile:last_name'`);
    this.meta.removeTag(`name='profile:gender'`);
    this.meta.removeTag(`name='description'`);
    this.meta.removeTag(`name='author'`);
    this.meta.removeTag(`name='meta'`);
    this.$girlInfo.subscribe((girl) => {
      if (girl) {
        const gallery = girl.girlInfoById.queryGalleries.entities[0];
        const slideshow = gallery.fieldMediaSlideshow[0];
        const mediaEntity = slideshow.entity;

        const fieldMediaImage = mediaEntity.fieldMediaImage;
        const fieldCredit = (
          girl.girlInfoById.queryGalleries.entities[0] as any
        )?.fieldCredit;
        const description = (
          girl.girlInfoById.queryGalleries.entities[0] as any
        )?.name;

        const imageUrl = fieldMediaImage.url;

        name;

        this.meta.addTag({
          name: 'og:title',
          content: girl.girlInfoById.queryGirl.entities[0].name,
        });
        this.meta.addTag({ name: 'og:description', content: description });
        this.meta.addTag({ name: 'description', content: description });
        this.meta.addTag({ name: 'og:image', content: imageUrl });
        this.meta.addTag({ name: 'og:author', content: fieldCredit });
      }
    });
    this.meta.addTag({ name: 'og:site_name', content: 'Playboy All Access' });
    this.meta.addTag({ name: 'og:type', content: 'article' });
    this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
    const currentUrl = this.location.path();
    this.meta.addTag({
      name: 'og:url',
      content: `https://premium.playboy.de${currentUrl}`,
    });
  }

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
    this.itemsSubscription?.unsubscribe();
    document.body.classList.remove('overflow-hidden', 'bg-black');
  }

  goToIndex(index: number) {
    const items = this.itemsBehav.getValue();
    this.router.navigate(
      [
        '/girl-info',
        this.girlId,
        items[Math.max(0, Math.min(index, items.length - 1))].entity.mid,
      ],
      { queryParamsHandling: 'merge' },
    );
  }

  onAfterSlide = ({ index }: { index: number }) => {
    this.goToIndex(index);
  }

  checkReturnUrl() {
    if (!!this.return) {
      window.location.href = this.return;
    }
  }
}
