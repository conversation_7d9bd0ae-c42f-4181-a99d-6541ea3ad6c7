import { Component, HostBinding, input, Input, OnInit } from '@angular/core';

import { AsyncPipe, NgClass } from '@angular/common';
import { MonthPipe } from '@pb/ui';
import { IPreview } from '../../../models/preview';
import { AccountService } from '../../../services/account.service';
import { ArticlePreviewComponent } from '../article-preview/article-preview.component';
import { FavoriteStarComponent } from '../favorite-star/favorite-star.component';
import { FavoriteType } from '../../../services/favorites/favorites.service';
import { LockBadgeComponent } from '../lock-badge/lock-badge.component';
import { PaywallImage } from '../../../screens/model/screens/model/definitions/models';

@Component({
  selector: 'app-gallery-card',
  templateUrl: './gallery-card.component.html',
  styleUrls: ['./gallery-card.component.css'],
  animations: [],
  imports: [
    AsyncPipe,
    ArticlePreviewComponent,
    Ng<PERSON>lass,
    MonthPipe,
    FavoriteStarComponent,
    LockBadgeComponent,
  ],
})
export class GalleryCardComponent implements OnInit {
  @Input() image: string;
  previewDataImage: string;

  @Input() overwriteImageRatio: string | null = null;

  unmatchedImageRatio = false;
  @Input() set imageRatio(ratio: number | undefined) {
    this.localImageRatio = Math.min(1.5 / 1, ratio);
    this.unmatchedImageRatio = this.localImageRatio !== ratio;
    this.refreshFill();
  }
  get imageRatio(): number | undefined {
    return this.localImageRatio;
  }
  localImageRatio?: number;

  @Input() withoutCard = false;

  @Input() imageLowRes?: string;
  previewDataImageLowRes?: string;
  @Input() title: string;
  @Input() year: number | string;
  @Input() month: string;
  @Input() credit?: string;
  @Input() name: string;
  @Input() nexxID?: string;
  @Input() focalPoint?: { x: number; y: number };
  @Input() meta?: {
    images: number;
    videos: number;
    girlInfos: number;
  };
  @Input() inlineVideo?: boolean;
  @Input() isNew?: boolean;

  @Input() isAAContent = false;

  @Input() favoriteType?: FavoriteType | undefined;
  @Input() favoriteId?: string | undefined;

  @Input() set previewData(data: IPreview) {
    this.previewDataImage = data.image;
    this.previewDataImageLowRes = data.imageLowRes;
    this.title = data.title;
    this.year = data.year;
    this.month = data.month;
    this.name = data.text;
    this.credit = data.credit;
    this.nexxID = data.nexxID;
    this.focalPoint = data.focalPoint;
    this.isNew = data.isNew;
    this.meta = data.meta;
    this.imageRatio = data.imageRatio;
    if (data.girlInfoId) {
      this.favoriteType = 'girl-infos';
      this.favoriteId = '' + data.girlInfoId;
    } else if (data.girlId) {
      this.favoriteType = 'girl';
      this.favoriteId = '' + data.girlId;
    } else if (data.id) {
      this.favoriteType = data.nexxID ? 'video' : 'image';
      this.favoriteId = '' + data.id;
    } else {
      this.favoriteId = undefined;
      this.favoriteType = undefined;
    }
  }

  paywallImages = input<PaywallImage[]>();

  @HostBinding('class.auto-fill')
  autoFill = true;

  refreshFill() {
    this.autoFill = !this.unmatchedImageRatio && this._autoSize;
  }

  private _autoSize = true;
  @Input() set autoSize(autoSize: boolean) {
    this._autoSize = autoSize;
    this.refreshFill();
  }
  get autoSize(): boolean {
    return this._autoSize;
  }

  @HostBinding('class.group')
  @Input()
  twGroup = true;

  constructor(readonly accService: AccountService) {}

  ngOnInit(): void {}
}
